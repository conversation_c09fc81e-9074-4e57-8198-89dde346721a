"""
URL-based Data Manager for Sports Betting Analysis
Handles manual URL input and selective stat storage
"""
from database.models import (
    create_database, get_session, Team, Player, Game, 
    PlayerStat, TeamStat, BettingLine
)
from scrapers.espn_scraper import ESPNBoxScoreScraper
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, or_, desc, func
from datetime import datetime
import pandas as pd
from typing import List, Dict, Optional, Set
import json

class URLDataManager:
    def __init__(self, db_path="sports_betting.db"):
        self.engine = create_database(db_path)
        self.Session = sessionmaker(bind=self.engine)
        self.scraper = ESPNBoxScoreScraper()
    
    def get_session(self):
        """Get a new database session"""
        return self.Session()
    
    def preview_url(self, url: str) -> Dict:
        """Preview what data can be extracted from a URL"""
        return self.scraper.preview_url_data(url)
    
    def scrape_url_data(self, url: str) -> Dict:
        """Scrape data from ESPN URL"""
        return self.scraper.scrape_boxscore_from_url(url)
    
    def get_available_stats(self, sport: str) -> Dict:
        """Get available betting stats for a sport"""
        return self.scraper.get_available_stats(sport)
    
    def store_game_from_url(self, url: str, selected_stats: Dict = None) -> Dict:
        """
        Store game data from URL with selected stats
        selected_stats format: {
            'player_stats': ['goals', 'assists', 'points'],
            'team_stats': ['goals', 'shots', 'hits']
        }
        """
        # Scrape the data
        scraped_data = self.scrape_url_data(url)
        
        if 'error' in scraped_data:
            return {"error": scraped_data['error']}
        
        session = self.get_session()
        try:
            # Check if game already exists
            existing_game = session.query(Game).filter_by(
                espn_game_id=scraped_data['game_id']
            ).first()
            
            if existing_game:
                return {"message": "Game already exists", "game_id": existing_game.id}
            
            # Create or get teams
            teams_data = scraped_data.get('teams', {})
            home_team = self._get_or_create_team(session, teams_data.get('home_team'), scraped_data['sport'])
            away_team = self._get_or_create_team(session, teams_data.get('away_team'), scraped_data['sport'])
            
            if not home_team or not away_team:
                return {"error": "Could not identify teams"}
            
            # Create game record
            game_info = scraped_data.get('game_info', {})
            final_score = game_info.get('final_score', [])
            
            game = Game(
                espn_game_id=scraped_data['game_id'],
                date=datetime.now(),  # You might want to parse actual date from game_info
                home_team_id=home_team.id,
                away_team_id=away_team.id,
                home_score=final_score[1] if len(final_score) > 1 else None,
                away_score=final_score[0] if len(final_score) > 0 else None,
                status='final',
                sport=scraped_data['sport'],
                venue=game_info.get('venue')
            )
            
            session.add(game)
            session.flush()  # Get the game ID
            
            # Store selected player stats
            if selected_stats and 'player_stats' in selected_stats:
                self._store_selected_player_stats(
                    session, game, scraped_data.get('player_stats', []), 
                    selected_stats['player_stats']
                )
            
            # Store selected team stats
            if selected_stats and 'team_stats' in selected_stats:
                self._store_selected_team_stats(
                    session, game, scraped_data.get('team_stats', {}),
                    selected_stats['team_stats']
                )
            
            session.commit()
            
            return {
                "success": True,
                "game_id": game.id,
                "sport": scraped_data['sport'],
                "teams": f"{away_team.name} @ {home_team.name}",
                "stats_stored": selected_stats or "all available"
            }
            
        except Exception as e:
            session.rollback()
            return {"error": f"Database error: {str(e)}"}
        finally:
            session.close()
    
    def _get_or_create_team(self, session, team_name: str, sport: str) -> Optional[Team]:
        """Get existing team or create new one"""
        if not team_name:
            return None
        
        # Try to find existing team
        team = session.query(Team).filter_by(name=team_name).first()
        
        if not team:
            # Create new team
            team = Team(
                name=team_name,
                abbreviation=team_name[:3].upper(),  # Simple abbreviation
                sport=sport
            )
            session.add(team)
            session.flush()
        
        return team
    
    def _store_selected_player_stats(self, session, game: Game, player_stats: List[Dict], selected_stats: List[str]):
        """Store only selected player statistics"""
        for player_data in player_stats:
            player_name = player_data.get('player_name')
            if not player_name:
                continue
            
            # Get or create player
            player = self._get_or_create_player(session, player_name, game.home_team_id)
            if not player:
                continue
            
            # Create player stat record with only selected stats
            stat_record = PlayerStat(
                game_id=game.id,
                player_id=player.id
            )
            
            # Map selected stats to database fields
            for stat_name in selected_stats:
                if stat_name in player_data:
                    value = player_data[stat_name]
                    if hasattr(stat_record, stat_name):
                        setattr(stat_record, stat_name, value)
            
            session.add(stat_record)
    
    def _get_or_create_player(self, session, player_name: str, team_id: int) -> Optional[Player]:
        """Get existing player or create new one"""
        if not player_name:
            return None
        
        # Try to find existing player
        player = session.query(Player).filter_by(name=player_name).first()
        
        if not player:
            # Create new player
            player = Player(
                name=player_name,
                team_id=team_id
            )
            session.add(player)
            session.flush()
        
        return player
    
    def _store_selected_team_stats(self, session, game: Game, team_stats: Dict, selected_stats: List[str]):
        """Store only selected team statistics"""
        for stat_name in selected_stats:
            if stat_name in team_stats:
                stat_data = team_stats[stat_name]
                
                if isinstance(stat_data, dict) and 'home' in stat_data and 'away' in stat_data:
                    # Store home team stats
                    home_stat = TeamStat(
                        game_id=game.id,
                        team_id=game.home_team_id
                    )
                    if hasattr(home_stat, stat_name):
                        setattr(home_stat, stat_name, stat_data['home'])
                    session.add(home_stat)
                    
                    # Store away team stats
                    away_stat = TeamStat(
                        game_id=game.id,
                        team_id=game.away_team_id
                    )
                    if hasattr(away_stat, stat_name):
                        setattr(away_stat, stat_name, stat_data['away'])
                    session.add(away_stat)
    
    def search_players(self, query: str, limit: int = 20) -> List[Dict]:
        """Search for players by name"""
        session = self.get_session()
        try:
            players = session.query(Player).filter(
                Player.name.ilike(f'%{query}%')
            ).limit(limit).all()
            
            results = []
            for player in players:
                team = session.query(Team).get(player.team_id)
                results.append({
                    'id': player.id,
                    'name': player.name,
                    'team': team.name if team else 'Unknown',
                    'position': player.position
                })
            
            return results
        finally:
            session.close()
    
    def search_teams(self, query: str, sport: str = None, limit: int = 20) -> List[Dict]:
        """Search for teams by name or abbreviation"""
        session = self.get_session()
        try:
            query_filter = or_(
                Team.name.ilike(f'%{query}%'),
                Team.abbreviation.ilike(f'%{query}%')
            )
            
            if sport:
                query_filter = and_(query_filter, Team.sport == sport)
            
            teams = session.query(Team).filter(query_filter).limit(limit).all()
            
            results = []
            for team in teams:
                # Count games for this team
                game_count = session.query(Game).filter(
                    or_(Game.home_team_id == team.id, Game.away_team_id == team.id)
                ).count()
                
                results.append({
                    'id': team.id,
                    'name': team.name,
                    'abbreviation': team.abbreviation,
                    'sport': getattr(team, 'sport', 'Unknown'),
                    'games_count': game_count
                })
            
            return results
        finally:
            session.close()
    
    def get_player_stats_summary(self, player_id: int, stat_types: List[str] = None) -> Dict:
        """Get statistical summary for a player"""
        session = self.get_session()
        try:
            player = session.query(Player).get(player_id)
            if not player:
                return {"error": "Player not found"}
            
            # Get all stats for this player
            stats = session.query(PlayerStat).filter_by(player_id=player_id).all()
            
            if not stats:
                return {"error": "No stats found for player"}
            
            # Calculate averages and totals
            summary = {
                'player_name': player.name,
                'games_played': len(stats),
                'stats': {}
            }
            
            # Define which stats to summarize
            if not stat_types:
                stat_types = ['goals', 'assists', 'points', 'shots', 'rebounds', 'steals', 'blocks']
            
            for stat_type in stat_types:
                values = []
                for stat in stats:
                    value = getattr(stat, stat_type, None)
                    if value is not None:
                        values.append(float(value))
                
                if values:
                    summary['stats'][stat_type] = {
                        'total': sum(values),
                        'average': sum(values) / len(values),
                        'max': max(values),
                        'min': min(values)
                    }
            
            return summary
        finally:
            session.close()
    
    def get_team_stats_summary(self, team_id: int, stat_types: List[str] = None) -> Dict:
        """Get statistical summary for a team"""
        session = self.get_session()
        try:
            team = session.query(Team).get(team_id)
            if not team:
                return {"error": "Team not found"}
            
            # Get all games for this team
            games = session.query(Game).filter(
                or_(Game.home_team_id == team_id, Game.away_team_id == team_id)
            ).all()
            
            if not games:
                return {"error": "No games found for team"}
            
            summary = {
                'team_name': team.name,
                'games_played': len(games),
                'wins': 0,
                'losses': 0,
                'stats': {}
            }
            
            # Calculate win/loss record
            for game in games:
                if game.home_score is not None and game.away_score is not None:
                    if team_id == game.home_team_id:
                        if game.home_score > game.away_score:
                            summary['wins'] += 1
                        else:
                            summary['losses'] += 1
                    else:
                        if game.away_score > game.home_score:
                            summary['wins'] += 1
                        else:
                            summary['losses'] += 1
            
            return summary
        finally:
            session.close()
    
    def list_stored_games(self, sport: str = None, limit: int = 50) -> List[Dict]:
        """List all stored games"""
        session = self.get_session()
        try:
            query = session.query(Game)
            
            if sport:
                query = query.filter(Game.sport == sport)
            
            games = query.order_by(desc(Game.date)).limit(limit).all()
            
            results = []
            for game in games:
                home_team = session.query(Team).get(game.home_team_id)
                away_team = session.query(Team).get(game.away_team_id)
                
                results.append({
                    'id': game.id,
                    'espn_id': game.espn_game_id,
                    'date': game.date.isoformat() if game.date else None,
                    'home_team': home_team.name if home_team else 'Unknown',
                    'away_team': away_team.name if away_team else 'Unknown',
                    'score': f"{game.away_score}-{game.home_score}" if game.home_score and game.away_score else "N/A",
                    'sport': getattr(game, 'sport', 'Unknown')
                })
            
            return results
        finally:
            session.close()
