"""
Test script to demonstrate the MongoDB sports betting system
"""
from mongo_data_manager import MongoDataManager
from datetime import datetime

def add_sample_mlb_data(dm):
    """Add sample MLB data to test the system"""
    print("Adding sample MLB data...")
    
    # Sample game data structure
    sample_game = {
        'url': 'https://www.espn.com/mlb/boxscore/_/gameId/401695669',
        'sport': 'mlb',
        'game_id': '401695669',
        'game_info': {
            'final_score': [5, 8],  # Away: 5, Home: 8
            'venue': 'Sample Stadium'
        },
        'teams': {
            'home_team': 'Los Angeles Dodgers',
            'away_team': 'San Francisco Giants'
        },
        'player_stats': [
            {
                'player_name': '<PERSON><PERSON><PERSON>',
                'hits': 2,
                'home_runs': 1,
                'rbis': 3,
                'strikeouts_pitcher': 0,
                'hits_allowed': 0
            },
            {
                'player_name': '<PERSON>',
                'hits': 1,
                'home_runs': 0,
                'rbis': 1,
                'strikeouts_pitcher': 0,
                'hits_allowed': 0
            },
            {
                'player_name': '<PERSON>',
                'hits': 0,
                'home_runs': 0,
                'rbis': 0,
                'strikeouts_pitcher': 8,
                'hits_allowed': 4
            },
            {
                'player_name': 'Mike Yastrzemski',
                'hits': 1,
                'home_runs': 0,
                'rbis': 2,
                'strikeouts_pitcher': 0,
                'hits_allowed': 0
            },
            {
                'player_name': 'Logan <PERSON>',
                'hits': 0,
                'home_runs': 0,
                'rbis': 0,
                'strikeouts_pitcher': 6,
                'hits_allowed': 7
            }
        ]
    }
    
    # Your preferred stats
    selected_stats = {
        'player_stats': ['hits', 'home_runs', 'rbis', 'strikeouts_pitcher', 'hits_allowed']
    }
    
    # Manually store the data
    try:
        # Create teams
        home_team = dm._get_or_create_team(sample_game['teams']['home_team'], 'mlb')
        away_team = dm._get_or_create_team(sample_game['teams']['away_team'], 'mlb')
        
        # Create game
        game_doc = {
            "espn_game_id": sample_game['game_id'],
            "url": sample_game['url'],
            "sport": sample_game['sport'],
            "date": datetime.now(),
            "home_team": {
                "id": home_team['_id'],
                "name": home_team['name']
            },
            "away_team": {
                "id": away_team['_id'],
                "name": away_team['name']
            },
            "home_score": sample_game['game_info']['final_score'][1],
            "away_score": sample_game['game_info']['final_score'][0],
            "status": "final",
            "venue": sample_game['game_info']['venue'],
            "created_at": datetime.now()
        }
        
        # Insert game
        game_result = dm.games.insert_one(game_doc)
        game_id = game_result.inserted_id
        
        # Store player stats
        stats_stored = dm._store_selected_player_stats(
            game_id, 
            sample_game['player_stats'], 
            selected_stats['player_stats'],
            'mlb',
            home_team,
            away_team
        )
        
        print(f"✅ Successfully added sample data!")
        print(f"Game ID: {game_id}")
        print(f"Teams: {away_team['name']} @ {home_team['name']}")
        print(f"Score: {sample_game['game_info']['final_score'][0]}-{sample_game['game_info']['final_score'][1]}")
        print(f"Player stats stored: {stats_stored}")
        
        return True
        
    except Exception as e:
        print(f"Error adding sample data: {e}")
        return False

def test_search_functions(dm):
    """Test the search functionality"""
    print("\n=== Testing Search Functions ===")
    
    # Search for players
    print("\n1. Searching for 'Betts':")
    players = dm.search_players("Betts", "mlb")
    for player in players:
        print(f"   - {player['name']} ({player['sport']})")
    
    # Search for teams
    print("\n2. Searching for 'Dodgers':")
    teams = dm.search_teams("Dodgers", "mlb")
    for team in teams:
        print(f"   - {team['name']} ({team['abbreviation']}) - {team['games_count']} games")
    
    # Get player stats
    if players:
        print(f"\n3. Stats for {players[0]['name']}:")
        stats = dm.get_player_stats_summary(players[0]['id'])
        if 'error' not in stats:
            print(f"   Games played: {stats['games_played']}")
            for stat_name, stat_data in stats.get('stats', {}).items():
                print(f"   {stat_name}: {stat_data['average']:.2f} avg, {stat_data['total']:.0f} total")

def test_database_stats(dm):
    """Test database statistics"""
    print("\n=== Database Statistics ===")
    stats = dm.get_database_stats()
    print(f"Connection: {stats['connection_status']}")
    print(f"Total games: {stats['total_games']}")
    print(f"Total players: {stats['total_players']}")
    print(f"Total teams: {stats['total_teams']}")
    print(f"Total player stats: {stats['total_player_stats']}")
    print(f"Sports: {', '.join(stats['sports']) if stats['sports'] else 'None'}")

def main():
    print("🏀 MongoDB Sports Betting System Test")
    print("=====================================")
    
    # Initialize data manager
    try:
        dm = MongoDataManager()
        print("✅ Connected to MongoDB")
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return
    
    # Test database stats (before)
    print("\n--- Before adding data ---")
    test_database_stats(dm)
    
    # Add sample data
    if add_sample_mlb_data(dm):
        # Test database stats (after)
        print("\n--- After adding data ---")
        test_database_stats(dm)
        
        # Test search functions
        test_search_functions(dm)
        
        # List games
        print("\n=== Stored Games ===")
        games = dm.list_stored_games("mlb")
        for game in games:
            print(f"- {game['away_team']} @ {game['home_team']} ({game['score']}) - {game['sport']}")
    
    # Close connection
    dm.close_connection()
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()
