"""
Main script for Sports Betting Analysis System
"""
from data_manager import DataManager, initialize_nba_teams
from analysis.trend_analyzer import <PERSON>rendAnalyzer
from datetime import datetime, timedelta
import argparse
import sys

def setup_database():
    """Initialize database and add NBA teams"""
    print("Setting up database...")
    dm = DataManager()
    initialize_nba_teams(dm)
    print("Database setup complete!")
    return dm

def scrape_recent_games(dm: DataManager, days_back: int = 7):
    """Scrape recent games"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')
    
    print(f"Scraping games from {start_str} to {end_str}...")
    dm.scrape_and_store_games(start_str, end_str)

def analyze_team(dm: DataManager, team_abbr: str):
    """Analyze a specific team"""
    analyzer = TrendAnalyzer(dm)
    analysis = analyzer.analyze_team_performance_trends(team_abbr, 15)
    
    print(f"\n=== {team_abbr} Analysis ===")
    if "error" in analysis:
        print(f"Error: {analysis['error']}")
        return
    
    print(f"Games analyzed: {analysis['games_analyzed']}")
    print(f"Win rate: {analysis['win_rate']:.1%}" if analysis['win_rate'] else "Win rate: N/A")
    print(f"Avg points scored: {analysis['avg_points_scored']:.1f}" if analysis['avg_points_scored'] else "Avg points: N/A")
    print(f"Avg point differential: {analysis['avg_point_differential']:.1f}" if analysis['avg_point_differential'] else "Avg point diff: N/A")
    
    # Recent form
    recent_form = analysis.get('recent_form', {})
    if recent_form:
        print(f"\nRecent form (last {recent_form.get('last_n_games', 5)} games):")
        print(f"  Win rate: {recent_form.get('win_rate', 0):.1%}")
        print(f"  Trend: {recent_form.get('trend', 'N/A')}")
    
    # Home vs Away
    home_away = analysis.get('home_vs_away', {})
    if home_away and 'home_record' in home_away:
        print(f"\nHome/Away splits:")
        home = home_away['home_record']
        away = home_away['away_record']
        print(f"  Home: {home.get('win_rate', 0):.1%} win rate ({home.get('games', 0)} games)")
        print(f"  Away: {away.get('win_rate', 0):.1%} win rate ({away.get('games', 0)} games)")

def find_opportunities(dm: DataManager):
    """Find betting opportunities"""
    analyzer = TrendAnalyzer(dm)
    opportunities = analyzer.find_betting_opportunities()
    
    print("\n=== Betting Opportunities ===")
    if not opportunities:
        print("No opportunities found with current data.")
        return
    
    for opp in opportunities:
        print(f"\n{opp['team']} - {opp['type'].upper()}")
        print(f"  Confidence: {opp['confidence']}")
        print(f"  Description: {opp['description']}")
        print(f"  Suggested bet: {opp['suggested_bet']}")

def show_team_trends(dm: DataManager, team_abbr: str):
    """Show recent trends for a team"""
    df = dm.get_team_trends(team_abbr, 10)
    
    if df.empty:
        print(f"No data found for {team_abbr}")
        return
    
    print(f"\n=== {team_abbr} Recent Games ===")
    print(df[['date', 'opponent', 'home_away', 'team_score', 'opponent_score', 'win']].to_string(index=False))

def main():
    parser = argparse.ArgumentParser(description='Sports Betting Analysis System')
    parser.add_argument('--setup', action='store_true', help='Setup database and initialize teams')
    parser.add_argument('--scrape', type=int, metavar='DAYS', help='Scrape games from last N days')
    parser.add_argument('--analyze', type=str, metavar='TEAM', help='Analyze specific team (e.g., LAL)')
    parser.add_argument('--trends', type=str, metavar='TEAM', help='Show recent trends for team')
    parser.add_argument('--opportunities', action='store_true', help='Find betting opportunities')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')
    
    args = parser.parse_args()
    
    # If no arguments provided, show help
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    # Initialize data manager
    dm = DataManager()
    
    if args.setup:
        dm = setup_database()
    
    if args.scrape:
        scrape_recent_games(dm, args.scrape)
    
    if args.analyze:
        analyze_team(dm, args.analyze.upper())
    
    if args.trends:
        show_team_trends(dm, args.trends.upper())
    
    if args.opportunities:
        find_opportunities(dm)
    
    if args.interactive:
        interactive_mode(dm)

def interactive_mode(dm: DataManager):
    """Run in interactive mode"""
    analyzer = TrendAnalyzer(dm)
    
    print("\n=== Sports Betting Analysis System ===")
    print("Available commands:")
    print("  1. analyze <TEAM> - Analyze team performance")
    print("  2. trends <TEAM> - Show recent trends")
    print("  3. opportunities - Find betting opportunities")
    print("  4. scrape <DAYS> - Scrape recent games")
    print("  5. quit - Exit")
    
    while True:
        try:
            command = input("\nEnter command: ").strip().split()
            
            if not command:
                continue
            
            if command[0].lower() == 'quit':
                break
            elif command[0].lower() == 'analyze' and len(command) > 1:
                analyze_team(dm, command[1].upper())
            elif command[0].lower() == 'trends' and len(command) > 1:
                show_team_trends(dm, command[1].upper())
            elif command[0].lower() == 'opportunities':
                find_opportunities(dm)
            elif command[0].lower() == 'scrape' and len(command) > 1:
                try:
                    days = int(command[1])
                    scrape_recent_games(dm, days)
                except ValueError:
                    print("Please provide a valid number of days")
            else:
                print("Invalid command. Type 'quit' to exit.")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
