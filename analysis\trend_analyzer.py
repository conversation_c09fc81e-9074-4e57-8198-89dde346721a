"""
Trend Analysis Module for Sports Betting
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
from data_manager import DataManager

class TrendAnalyzer:
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        
    def analyze_team_performance_trends(self, team_abbreviation: str, 
                                      last_n_games: int = 20) -> Dict:
        """Analyze various performance trends for a team"""
        df = self.data_manager.get_team_trends(team_abbreviation, last_n_games)
        
        if df.empty:
            return {"error": f"No data found for team {team_abbreviation}"}
        
        analysis = {
            "team": team_abbreviation,
            "games_analyzed": len(df),
            "win_rate": df['win'].mean() if 'win' in df.columns else None,
            "avg_points_scored": df['team_score'].mean() if 'team_score' in df.columns else None,
            "avg_points_allowed": df['opponent_score'].mean() if 'opponent_score' in df.columns else None,
            "avg_point_differential": df['point_differential'].mean() if 'point_differential' in df.columns else None,
            "home_vs_away": self._analyze_home_away_performance(df),
            "recent_form": self._analyze_recent_form(df),
            "scoring_trends": self._analyze_scoring_trends(df),
            "consistency": self._analyze_consistency(df)
        }
        
        return analysis
    
    def analyze_player_performance_trends(self, player_name: str, 
                                        last_n_games: int = 20) -> Dict:
        """Analyze performance trends for a specific player"""
        df = self.data_manager.get_player_trends(player_name, last_n_games)
        
        if df.empty:
            return {"error": f"No data found for player {player_name}"}
        
        analysis = {
            "player": player_name,
            "games_analyzed": len(df),
            "avg_points": df['points'].mean() if 'points' in df.columns else None,
            "avg_rebounds": df['rebounds'].mean() if 'rebounds' in df.columns else None,
            "avg_assists": df['assists'].mean() if 'assists' in df.columns else None,
            "avg_minutes": df['minutes'].mean() if 'minutes' in df.columns else None,
            "scoring_trends": self._analyze_player_scoring_trends(df),
            "consistency": self._analyze_player_consistency(df),
            "recent_form": self._analyze_player_recent_form(df)
        }
        
        return analysis
    
    def _analyze_home_away_performance(self, df: pd.DataFrame) -> Dict:
        """Analyze home vs away performance"""
        if 'home_away' not in df.columns:
            return {"error": "Home/Away data not available"}
        
        home_games = df[df['home_away'] == 'Home']
        away_games = df[df['home_away'] == 'Away']
        
        return {
            "home_record": {
                "games": len(home_games),
                "win_rate": home_games['win'].mean() if len(home_games) > 0 else None,
                "avg_points": home_games['team_score'].mean() if len(home_games) > 0 else None
            },
            "away_record": {
                "games": len(away_games),
                "win_rate": away_games['win'].mean() if len(away_games) > 0 else None,
                "avg_points": away_games['team_score'].mean() if len(away_games) > 0 else None
            }
        }
    
    def _analyze_recent_form(self, df: pd.DataFrame, recent_games: int = 5) -> Dict:
        """Analyze recent form (last N games)"""
        if len(df) < recent_games:
            recent_games = len(df)
        
        recent_df = df.head(recent_games)  # Most recent games first
        
        return {
            "last_n_games": recent_games,
            "win_rate": recent_df['win'].mean() if 'win' in recent_df.columns else None,
            "avg_points": recent_df['team_score'].mean() if 'team_score' in recent_df.columns else None,
            "avg_point_differential": recent_df['point_differential'].mean() if 'point_differential' in recent_df.columns else None,
            "trend": self._calculate_trend(recent_df['point_differential']) if 'point_differential' in recent_df.columns else None
        }
    
    def _analyze_scoring_trends(self, df: pd.DataFrame) -> Dict:
        """Analyze scoring trends over time"""
        if 'team_score' not in df.columns:
            return {"error": "Scoring data not available"}
        
        # Calculate moving averages
        df_sorted = df.sort_values('date')
        df_sorted['score_ma_3'] = df_sorted['team_score'].rolling(window=3).mean()
        df_sorted['score_ma_5'] = df_sorted['team_score'].rolling(window=5).mean()
        
        # Calculate trend
        games_numeric = range(len(df_sorted))
        trend_slope = self._calculate_linear_trend(games_numeric, df_sorted['team_score'])
        
        return {
            "current_avg": df['team_score'].mean(),
            "trend_slope": trend_slope,
            "trend_direction": "improving" if trend_slope > 0 else "declining" if trend_slope < 0 else "stable",
            "highest_score": df['team_score'].max(),
            "lowest_score": df['team_score'].min(),
            "score_volatility": df['team_score'].std()
        }
    
    def _analyze_consistency(self, df: pd.DataFrame) -> Dict:
        """Analyze team consistency"""
        if 'point_differential' not in df.columns:
            return {"error": "Point differential data not available"}
        
        return {
            "point_diff_std": df['point_differential'].std(),
            "consistency_rating": self._calculate_consistency_rating(df['point_differential']),
            "blowout_wins": len(df[(df['point_differential'] > 15) & (df['win'] == True)]),
            "close_games": len(df[abs(df['point_differential']) <= 5]),
            "blowout_losses": len(df[(df['point_differential'] < -15) & (df['win'] == False)])
        }
    
    def _analyze_player_scoring_trends(self, df: pd.DataFrame) -> Dict:
        """Analyze player scoring trends"""
        if 'points' not in df.columns:
            return {"error": "Scoring data not available"}
        
        games_numeric = range(len(df))
        trend_slope = self._calculate_linear_trend(games_numeric, df['points'])
        
        return {
            "avg_points": df['points'].mean(),
            "trend_slope": trend_slope,
            "trend_direction": "improving" if trend_slope > 0 else "declining" if trend_slope < 0 else "stable",
            "season_high": df['points'].max(),
            "season_low": df['points'].min(),
            "scoring_volatility": df['points'].std()
        }
    
    def _analyze_player_consistency(self, df: pd.DataFrame) -> Dict:
        """Analyze player consistency"""
        if 'points' not in df.columns:
            return {"error": "Points data not available"}
        
        return {
            "points_std": df['points'].std(),
            "consistency_rating": self._calculate_consistency_rating(df['points']),
            "double_digit_games": len(df[df['points'] >= 10]),
            "twenty_plus_games": len(df[df['points'] >= 20]),
            "single_digit_games": len(df[df['points'] < 10])
        }
    
    def _analyze_player_recent_form(self, df: pd.DataFrame, recent_games: int = 5) -> Dict:
        """Analyze player's recent form"""
        if len(df) < recent_games:
            recent_games = len(df)
        
        recent_df = df.head(recent_games)
        
        return {
            "last_n_games": recent_games,
            "avg_points": recent_df['points'].mean() if 'points' in recent_df.columns else None,
            "avg_rebounds": recent_df['rebounds'].mean() if 'rebounds' in recent_df.columns else None,
            "avg_assists": recent_df['assists'].mean() if 'assists' in recent_df.columns else None,
            "trend": self._calculate_trend(recent_df['points']) if 'points' in recent_df.columns else None
        }
    
    def _calculate_trend(self, series: pd.Series) -> str:
        """Calculate if a series is trending up, down, or stable"""
        if len(series) < 2:
            return "insufficient_data"
        
        games_numeric = range(len(series))
        slope = self._calculate_linear_trend(games_numeric, series)
        
        if slope > 0.5:
            return "strongly_improving"
        elif slope > 0.1:
            return "improving"
        elif slope < -0.5:
            return "strongly_declining"
        elif slope < -0.1:
            return "declining"
        else:
            return "stable"
    
    def _calculate_linear_trend(self, x: List, y: pd.Series) -> float:
        """Calculate linear trend slope"""
        try:
            x_array = np.array(x).reshape(-1, 1)
            y_array = np.array(y)
            
            # Remove NaN values
            mask = ~np.isnan(y_array)
            if mask.sum() < 2:
                return 0.0
            
            x_clean = x_array[mask]
            y_clean = y_array[mask]
            
            model = LinearRegression()
            model.fit(x_clean, y_clean)
            return model.coef_[0]
        except:
            return 0.0
    
    def _calculate_consistency_rating(self, series: pd.Series) -> str:
        """Calculate consistency rating based on standard deviation"""
        std = series.std()
        mean = series.mean()
        
        if mean == 0:
            return "no_data"
        
        cv = std / mean  # Coefficient of variation
        
        if cv < 0.15:
            return "very_consistent"
        elif cv < 0.25:
            return "consistent"
        elif cv < 0.35:
            return "moderately_consistent"
        elif cv < 0.5:
            return "inconsistent"
        else:
            return "very_inconsistent"
    
    def find_betting_opportunities(self, teams: List[str] = None) -> List[Dict]:
        """Find potential betting opportunities based on trends"""
        opportunities = []
        
        if not teams:
            # Get all teams from database
            session = self.data_manager.get_session()
            try:
                from database.models import Team
                all_teams = session.query(Team).all()
                teams = [team.abbreviation for team in all_teams]
            finally:
                session.close()
        
        for team in teams:
            analysis = self.analyze_team_performance_trends(team, 15)
            
            if "error" in analysis:
                continue
            
            # Look for specific patterns
            opportunity = self._identify_opportunity(team, analysis)
            if opportunity:
                opportunities.append(opportunity)
        
        return opportunities
    
    def _identify_opportunity(self, team: str, analysis: Dict) -> Optional[Dict]:
        """Identify betting opportunities from team analysis"""
        opportunities = []
        
        # Hot streak detection
        recent_form = analysis.get('recent_form', {})
        if recent_form.get('win_rate', 0) >= 0.8 and recent_form.get('trend') in ['improving', 'strongly_improving']:
            opportunities.append({
                "team": team,
                "type": "hot_streak",
                "confidence": "high",
                "description": f"{team} is on a hot streak with {recent_form.get('win_rate', 0):.1%} win rate in recent games",
                "suggested_bet": "moneyline"
            })
        
        # Undervalued team (good performance, poor recent record)
        if (analysis.get('avg_point_differential', 0) > 3 and 
            recent_form.get('win_rate', 0) < 0.4):
            opportunities.append({
                "team": team,
                "type": "undervalued",
                "confidence": "medium",
                "description": f"{team} has good point differential but poor recent record - potential value",
                "suggested_bet": "spread"
            })
        
        # Consistent over performer
        consistency = analysis.get('consistency', {})
        if (consistency.get('consistency_rating') in ['very_consistent', 'consistent'] and
            analysis.get('win_rate', 0) > 0.6):
            opportunities.append({
                "team": team,
                "type": "consistent_performer",
                "confidence": "medium",
                "description": f"{team} is consistently performing well",
                "suggested_bet": "moneyline"
            })
        
        return opportunities[0] if opportunities else None
    
    def create_trend_visualization(self, team_abbreviation: str, save_path: str = None):
        """Create visualization of team trends"""
        df = self.data_manager.get_team_trends(team_abbreviation, 20)
        
        if df.empty:
            print(f"No data available for {team_abbreviation}")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'{team_abbreviation} Performance Trends', fontsize=16)
        
        # Points scored over time
        axes[0, 0].plot(df.index, df['team_score'], marker='o')
        axes[0, 0].set_title('Points Scored per Game')
        axes[0, 0].set_ylabel('Points')
        
        # Point differential over time
        axes[0, 1].plot(df.index, df['point_differential'], marker='o', color='green')
        axes[0, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 1].set_title('Point Differential per Game')
        axes[0, 1].set_ylabel('Point Differential')
        
        # Win/Loss pattern
        wins = df['win'].astype(int)
        axes[1, 0].bar(df.index, wins, color=['red' if w == 0 else 'green' for w in wins])
        axes[1, 0].set_title('Win/Loss Pattern')
        axes[1, 0].set_ylabel('Win (1) / Loss (0)')
        
        # Home vs Away performance
        home_away_data = df.groupby('home_away').agg({
            'win': 'mean',
            'team_score': 'mean'
        })
        
        if len(home_away_data) > 1:
            axes[1, 1].bar(home_away_data.index, home_away_data['win'], alpha=0.7)
            axes[1, 1].set_title('Home vs Away Win Rate')
            axes[1, 1].set_ylabel('Win Rate')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()

# Example usage
if __name__ == "__main__":
    # Initialize data manager
    dm = DataManager()
    analyzer = TrendAnalyzer(dm)
    
    # Example analysis
    team_analysis = analyzer.analyze_team_performance_trends("LAL", 15)
    print("Lakers Analysis:")
    print(team_analysis)
    
    # Find betting opportunities
    opportunities = analyzer.find_betting_opportunities(["LAL", "GSW", "BOS"])
    print("\nBetting Opportunities:")
    for opp in opportunities:
        print(f"- {opp}")
