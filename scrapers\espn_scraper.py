"""
ESPN Box Score Scraper - URL-based scraping for manual input
"""
import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import re
from typing import Dict, List, Optional, Tuple
import json

class ESPNBoxScoreScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        # Define betting-relevant stats by sport
        self.betting_stats = {
            'nhl': {
                'player_stats': ['goals', 'assists', 'points', 'shots', 'hits', 'blocked_shots', 'penalty_minutes', 'plus_minus', 'faceoff_wins', 'faceoff_attempts', 'time_on_ice'],
                'goalie_stats': ['saves', 'shots_against', 'goals_allowed', 'save_percentage', 'time_on_ice'],
                'team_stats': ['goals', 'shots', 'hits', 'blocked_shots', 'faceoff_percentage', 'penalty_minutes', 'power_play_goals', 'power_play_opportunities']
            },
            'nba': {
                'player_stats': ['points', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers', 'field_goals_made', 'field_goals_attempted', 'three_pointers_made', 'three_pointers_attempted', 'free_throws_made', 'free_throws_attempted', 'minutes'],
                'team_stats': ['points', 'field_goal_percentage', 'three_point_percentage', 'free_throw_percentage', 'rebounds', 'assists', 'turnovers', 'steals', 'blocks']
            },
            'nfl': {
                'player_stats': ['passing_yards', 'passing_touchdowns', 'interceptions', 'rushing_yards', 'rushing_touchdowns', 'receiving_yards', 'receiving_touchdowns', 'receptions', 'tackles', 'sacks'],
                'team_stats': ['total_yards', 'passing_yards', 'rushing_yards', 'turnovers', 'penalties', 'time_of_possession']
            },
            'mlb': {
                'player_stats': ['hits', 'runs', 'rbis', 'home_runs', 'stolen_bases', 'strikeouts_batter', 'walks_batter', 'innings_pitched', 'strikeouts_pitcher', 'walks_pitcher', 'earned_runs'],
                'team_stats': ['runs', 'hits', 'errors', 'left_on_base']
            }
        }

    def parse_espn_url(self, url: str) -> Tuple[str, str]:
        """
        Parse ESPN URL to extract sport and game ID
        Example: https://www.espn.com/nhl/boxscore/_/gameId/401773322
        Returns: (sport, game_id)
        """
        try:
            # Extract sport and game ID from URL
            url_pattern = r'https://www\.espn\.com/(\w+)/boxscore/_/gameId/(\d+)'
            match = re.search(url_pattern, url)

            if match:
                sport = match.group(1)
                game_id = match.group(2)
                return sport, game_id
            else:
                raise ValueError("Invalid ESPN boxscore URL format")

        except Exception as e:
            print(f"Error parsing ESPN URL: {e}")
            return None, None

    def scrape_boxscore_from_url(self, url: str) -> Dict:
        """
        Scrape box score data from ESPN URL
        """
        sport, game_id = self.parse_espn_url(url)

        if not sport or not game_id:
            return {"error": "Could not parse URL"}

        try:
            response = self.session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract basic game info
            game_info = self._extract_game_info(soup, sport, game_id)

            # Extract team info
            teams = self._extract_teams(soup, sport)

            # Extract player stats
            player_stats = self._extract_player_stats(soup, sport)

            # Extract team stats
            team_stats = self._extract_team_stats(soup, sport)

            return {
                'url': url,
                'sport': sport,
                'game_id': game_id,
                'game_info': game_info,
                'teams': teams,
                'player_stats': player_stats,
                'team_stats': team_stats,
                'available_stats': self.betting_stats.get(sport, {}),
                'scraped_at': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Error scraping boxscore from {url}: {e}")
            return {"error": str(e)}

    def _extract_game_info(self, soup: BeautifulSoup, sport: str, game_id: str) -> Dict:
        """Extract basic game information"""
        game_info = {'game_id': game_id, 'sport': sport}

        try:
            # Extract game date and time
            game_header = soup.find('div', class_='GameInfo') or soup.find('div', class_='game-info')
            if game_header:
                date_element = game_header.find('span') or game_header.find('div')
                if date_element:
                    game_info['date_time'] = date_element.get_text(strip=True)

            # Extract venue
            venue_elements = soup.find_all(string=re.compile(r'@|at '))
            for element in venue_elements:
                if element.parent:
                    game_info['venue'] = element.parent.get_text(strip=True)
                    break

            # Extract final score
            score_elements = soup.find_all('div', class_=re.compile(r'score|final'))
            scores = []
            for score_elem in score_elements:
                score_text = score_elem.get_text(strip=True)
                if score_text.isdigit():
                    scores.append(int(score_text))

            if len(scores) >= 2:
                game_info['final_score'] = scores[:2]

        except Exception as e:
            print(f"Error extracting game info: {e}")

        return game_info

    def _extract_teams(self, soup: BeautifulSoup, sport: str) -> Dict:
        """Extract team information"""
        teams = {}

        try:
            # Look for team names in various locations
            team_elements = soup.find_all(['h2', 'h3', 'span'], class_=re.compile(r'team|club'))
            team_names = []

            for elem in team_elements:
                text = elem.get_text(strip=True)
                if text and len(text) > 2 and text not in team_names:
                    team_names.append(text)

            # Also look for team abbreviations
            abbr_elements = soup.find_all('span', class_=re.compile(r'abbrev|short'))
            for elem in abbr_elements:
                text = elem.get_text(strip=True)
                if text and len(text) <= 5:
                    team_names.append(text)

            # Assign teams (typically away team first, then home team)
            if len(team_names) >= 2:
                teams['away_team'] = team_names[0]
                teams['home_team'] = team_names[1]

        except Exception as e:
            print(f"Error extracting teams: {e}")

        return teams

    def _extract_player_stats(self, soup: BeautifulSoup, sport: str) -> List[Dict]:
        """Extract player statistics from box score"""
        player_stats = []

        try:
            # Find all stat tables
            tables = soup.find_all('table') or soup.find_all('div', class_=re.compile(r'table|stats'))

            for table in tables:
                # Try to find header row
                header_row = table.find('thead') or table.find('tr')
                if not header_row:
                    continue

                headers = []
                header_cells = header_row.find_all(['th', 'td'])
                for cell in header_cells:
                    header_text = cell.get_text(strip=True).lower()
                    headers.append(header_text)

                # Find data rows
                tbody = table.find('tbody') or table
                rows = tbody.find_all('tr')[1:] if tbody.find_all('tr') else []

                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 2:
                        continue

                    player_data = {}

                    # First cell is usually player name
                    player_name_cell = cells[0]
                    player_name = player_name_cell.get_text(strip=True)

                    if not player_name or player_name.lower() in ['total', 'totals', 'team']:
                        continue

                    player_data['player_name'] = player_name

                    # Map remaining cells to headers
                    for i, cell in enumerate(cells[1:], 1):
                        if i < len(headers):
                            stat_name = headers[i]
                            stat_value = cell.get_text(strip=True)

                            # Clean up stat names and values
                            stat_name = self._clean_stat_name(stat_name)
                            stat_value = self._clean_stat_value(stat_value)

                            if stat_name and stat_value is not None:
                                player_data[stat_name] = stat_value

                    if len(player_data) > 1:  # More than just player name
                        player_stats.append(player_data)

        except Exception as e:
            print(f"Error extracting player stats: {e}")

        return player_stats

    def _extract_team_stats(self, soup: BeautifulSoup, sport: str) -> Dict:
        """Extract team statistics from box score"""
        team_stats = {}

        try:
            # Look for team stats sections
            team_stat_sections = soup.find_all(['div', 'section'], class_=re.compile(r'team.*stat|stat.*team'))

            for section in team_stat_sections:
                # Find stat rows
                stat_rows = section.find_all('tr') or section.find_all('div', class_=re.compile(r'stat.*row'))

                for row in stat_rows:
                    cells = row.find_all(['td', 'th', 'span', 'div'])
                    if len(cells) >= 3:
                        stat_name = cells[0].get_text(strip=True)
                        away_value = cells[1].get_text(strip=True)
                        home_value = cells[2].get_text(strip=True)

                        if stat_name:
                            team_stats[self._clean_stat_name(stat_name)] = {
                                'away': self._clean_stat_value(away_value),
                                'home': self._clean_stat_value(home_value)
                            }

        except Exception as e:
            print(f"Error extracting team stats: {e}")

        return team_stats

    def _clean_stat_name(self, stat_name: str) -> str:
        """Clean and standardize stat names"""
        if not stat_name:
            return ""

        # Convert to lowercase and replace spaces/special chars with underscores
        cleaned = re.sub(r'[^a-zA-Z0-9]', '_', stat_name.lower())
        cleaned = re.sub(r'_+', '_', cleaned).strip('_')

        # Map common variations to standard names
        stat_mappings = {
            'g': 'goals',
            'a': 'assists',
            'pts': 'points',
            'sog': 'shots',
            'pim': 'penalty_minutes',
            'toi': 'time_on_ice',
            'sv': 'saves',
            'sa': 'shots_against',
            'ga': 'goals_allowed',
            'sv_': 'save_percentage',
            'reb': 'rebounds',
            'ast': 'assists',
            'stl': 'steals',
            'blk': 'blocks',
            'to': 'turnovers',
            'fg': 'field_goals',
            'fga': 'field_goals_attempted',
            '3p': 'three_pointers',
            '3pa': 'three_pointers_attempted',
            'ft': 'free_throws',
            'fta': 'free_throws_attempted',
            'min': 'minutes'
        }

        return stat_mappings.get(cleaned, cleaned)

    def _clean_stat_value(self, stat_value: str):
        """Clean and convert stat values to appropriate types"""
        if not stat_value or stat_value in ['-', '--', 'N/A', '']:
            return None

        # Remove extra whitespace
        stat_value = stat_value.strip()

        # Handle time format (MM:SS)
        if ':' in stat_value:
            try:
                parts = stat_value.split(':')
                if len(parts) == 2:
                    minutes = int(parts[0])
                    seconds = int(parts[1])
                    return minutes + (seconds / 60.0)  # Convert to decimal minutes
            except:
                pass

        # Handle percentage
        if '%' in stat_value:
            try:
                return float(stat_value.replace('%', '')) / 100.0
            except:
                pass

        # Handle fractions (like 2/4)
        if '/' in stat_value:
            try:
                parts = stat_value.split('/')
                if len(parts) == 2:
                    return f"{parts[0]}/{parts[1]}"  # Keep as string for now
            except:
                pass

        # Try to convert to number
        try:
            if '.' in stat_value:
                return float(stat_value)
            else:
                return int(stat_value)
        except:
            return stat_value  # Return as string if can't convert

    def get_available_stats(self, sport: str) -> Dict:
        """Get available betting stats for a sport"""
        return self.betting_stats.get(sport, {})

    def preview_url_data(self, url: str) -> Dict:
        """Preview what data would be extracted from URL without full processing"""
        sport, game_id = self.parse_espn_url(url)

        if not sport or not game_id:
            return {"error": "Could not parse URL"}

        return {
            'sport': sport,
            'game_id': game_id,
            'available_betting_stats': self.betting_stats.get(sport, {}),
            'url_valid': True
        }

# Example usage
if __name__ == "__main__":
    scraper = ESPNBoxScoreScraper()

    # Test URL parsing
    test_url = "https://www.espn.com/nhl/boxscore/_/gameId/401773322"

    # Preview what stats are available
    preview = scraper.preview_url_data(test_url)
    print("URL Preview:", preview)

    # Scrape the actual data
    data = scraper.scrape_boxscore_from_url(test_url)
    print(f"Scraped data keys: {list(data.keys())}")

    if 'player_stats' in data:
        print(f"Found {len(data['player_stats'])} player stat entries")

    if 'available_stats' in data:
        print(f"Available betting stats for {data.get('sport', 'unknown')}: {data['available_stats']}")
