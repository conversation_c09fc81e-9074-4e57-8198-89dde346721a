"""
ESPN Box Score Scraper
"""
import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime, timedelta
import time
import re
from typing import Dict, List, Optional
import json

class ESPNScraper:
    def __init__(self):
        self.base_url = "https://www.espn.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_nba_schedule(self, date: str = None) -> List[Dict]:
        """
        Get NBA games for a specific date
        date format: YYYYMMDD (e.g., '20231215')
        """
        if not date:
            date = datetime.now().strftime('%Y%m%d')
        
        url = f"{self.base_url}/nba/schedule/_/date/{date}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            
            games = []
            game_elements = soup.find_all('div', class_='Table__TR Table__TR--sm')
            
            for game_element in game_elements:
                game_data = self._parse_game_element(game_element, date)
                if game_data:
                    games.append(game_data)
            
            return games
            
        except Exception as e:
            print(f"Error scraping NBA schedule for {date}: {e}")
            return []
    
    def get_game_boxscore(self, game_id: str) -> Dict:
        """
        Get detailed box score for a specific game
        """
        url = f"{self.base_url}/nba/boxscore/_/gameId/{game_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            
            boxscore_data = {
                'game_id': game_id,
                'teams': self._parse_team_info(soup),
                'player_stats': self._parse_player_stats(soup),
                'team_stats': self._parse_team_stats(soup),
                'game_info': self._parse_game_info(soup)
            }
            
            return boxscore_data
            
        except Exception as e:
            print(f"Error scraping boxscore for game {game_id}: {e}")
            return {}
    
    def _parse_game_element(self, element, date: str) -> Optional[Dict]:
        """Parse individual game element from schedule"""
        try:
            # Extract team names and game ID
            teams = element.find_all('span', class_='Table__Team')
            if len(teams) < 2:
                return None
            
            away_team = teams[0].text.strip()
            home_team = teams[1].text.strip()
            
            # Try to find game ID from links
            game_link = element.find('a', href=re.compile(r'/nba/game/'))
            game_id = None
            if game_link:
                href = game_link.get('href')
                game_id_match = re.search(r'/game/_/gameId/(\d+)', href)
                if game_id_match:
                    game_id = game_id_match.group(1)
            
            # Extract score if game is completed
            score_element = element.find('span', class_='Table__Score')
            scores = None
            if score_element:
                score_text = score_element.text.strip()
                score_match = re.search(r'(\d+)-(\d+)', score_text)
                if score_match:
                    scores = {
                        'away_score': int(score_match.group(1)),
                        'home_score': int(score_match.group(2))
                    }
            
            return {
                'game_id': game_id,
                'date': date,
                'away_team': away_team,
                'home_team': home_team,
                'scores': scores
            }
            
        except Exception as e:
            print(f"Error parsing game element: {e}")
            return None
    
    def _parse_team_info(self, soup) -> Dict:
        """Parse team information from boxscore"""
        teams = {}
        try:
            team_headers = soup.find_all('div', class_='Boxscore__Team')
            for i, header in enumerate(team_headers[:2]):
                team_name = header.find('h2').text.strip() if header.find('h2') else f"Team {i+1}"
                teams[f'team_{i+1}'] = {
                    'name': team_name,
                    'abbreviation': team_name.split()[-1] if team_name else None
                }
        except Exception as e:
            print(f"Error parsing team info: {e}")
        
        return teams
    
    def _parse_player_stats(self, soup) -> List[Dict]:
        """Parse player statistics from boxscore"""
        player_stats = []
        
        try:
            # Find all player stat tables
            stat_tables = soup.find_all('div', class_='Boxscore__Team')
            
            for team_idx, team_table in enumerate(stat_tables):
                table = team_table.find('table')
                if not table:
                    continue
                
                headers = [th.text.strip() for th in table.find('thead').find_all('th')]
                rows = table.find('tbody').find_all('tr') if table.find('tbody') else []
                
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= len(headers):
                        player_data = {'team_index': team_idx}
                        for i, header in enumerate(headers):
                            if i < len(cells):
                                player_data[header.lower().replace(' ', '_')] = cells[i].text.strip()
                        
                        player_stats.append(player_data)
        
        except Exception as e:
            print(f"Error parsing player stats: {e}")
        
        return player_stats
    
    def _parse_team_stats(self, soup) -> Dict:
        """Parse team statistics from boxscore"""
        team_stats = {}
        
        try:
            # Look for team stats section
            stats_section = soup.find('section', class_='Boxscore__TeamStats')
            if stats_section:
                stat_rows = stats_section.find_all('tr')
                for row in stat_rows:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        stat_name = cells[0].text.strip()
                        away_value = cells[1].text.strip()
                        home_value = cells[2].text.strip()
                        
                        team_stats[stat_name] = {
                            'away': away_value,
                            'home': home_value
                        }
        
        except Exception as e:
            print(f"Error parsing team stats: {e}")
        
        return team_stats
    
    def _parse_game_info(self, soup) -> Dict:
        """Parse general game information"""
        game_info = {}
        
        try:
            # Extract game date and time
            game_header = soup.find('div', class_='GameInfo')
            if game_header:
                date_element = game_header.find('span', class_='GameInfo__Meta')
                if date_element:
                    game_info['date_time'] = date_element.text.strip()
            
            # Extract venue information
            venue_element = soup.find('div', class_='GameInfo__Location')
            if venue_element:
                game_info['venue'] = venue_element.text.strip()
        
        except Exception as e:
            print(f"Error parsing game info: {e}")
        
        return game_info
    
    def scrape_date_range(self, start_date: str, end_date: str) -> List[Dict]:
        """
        Scrape games for a date range
        """
        all_games = []
        current_date = datetime.strptime(start_date, '%Y%m%d')
        end_date_obj = datetime.strptime(end_date, '%Y%m%d')
        
        while current_date <= end_date_obj:
            date_str = current_date.strftime('%Y%m%d')
            print(f"Scraping games for {date_str}...")
            
            games = self.get_nba_schedule(date_str)
            all_games.extend(games)
            
            # Be respectful to ESPN's servers
            time.sleep(1)
            
            current_date += timedelta(days=1)
        
        return all_games

# Example usage
if __name__ == "__main__":
    scraper = ESPNScraper()
    
    # Get today's games
    today = datetime.now().strftime('%Y%m%d')
    games = scraper.get_nba_schedule(today)
    print(f"Found {len(games)} games for {today}")
    
    # Get boxscore for a specific game (if available)
    if games and games[0]['game_id']:
        boxscore = scraper.get_game_boxscore(games[0]['game_id'])
        print(f"Boxscore data keys: {boxscore.keys()}")
