"""
URL-based Sports Betting Analysis System
Main interface for manual URL input and selective stat storage
"""
from url_data_manager import URLDataManager
from scrapers.espn_scraper import ESPNBoxScoreScraper
import argparse
import sys
import json

def preview_url(dm: URLDataManager, url: str):
    """Preview what data can be extracted from a URL"""
    print(f"\n=== URL Preview ===")
    print(f"URL: {url}")
    
    preview = dm.preview_url(url)
    
    if 'error' in preview:
        print(f"Error: {preview['error']}")
        return
    
    print(f"Sport: {preview['sport']}")
    print(f"Game ID: {preview['game_id']}")
    print(f"URL Valid: {preview['url_valid']}")
    
    # Show available betting stats
    available_stats = preview.get('available_betting_stats', {})
    if available_stats:
        print(f"\n=== Available Betting Stats for {preview['sport'].upper()} ===")
        
        if 'player_stats' in available_stats:
            print(f"Player Stats: {', '.join(available_stats['player_stats'])}")
        
        if 'goalie_stats' in available_stats:
            print(f"Goalie Stats: {', '.join(available_stats['goalie_stats'])}")
        
        if 'team_stats' in available_stats:
            print(f"Team Stats: {', '.join(available_stats['team_stats'])}")

def scrape_and_store_url(dm: URLDataManager, url: str, selected_stats: dict = None):
    """Scrape URL and store selected data"""
    print(f"\n=== Scraping and Storing ===")
    print(f"URL: {url}")
    
    if selected_stats:
        print(f"Selected stats: {selected_stats}")
    else:
        print("Storing all available stats")
    
    result = dm.store_game_from_url(url, selected_stats)
    
    if 'error' in result:
        print(f"Error: {result['error']}")
        return
    
    if 'success' in result:
        print(f"✅ Successfully stored game!")
        print(f"Game ID: {result['game_id']}")
        print(f"Sport: {result['sport']}")
        print(f"Teams: {result['teams']}")
        print(f"Stats stored: {result['stats_stored']}")
    else:
        print(f"ℹ️ {result.get('message', 'Unknown result')}")

def search_players(dm: URLDataManager, query: str):
    """Search for players"""
    print(f"\n=== Player Search: '{query}' ===")
    
    results = dm.search_players(query)
    
    if not results:
        print("No players found")
        return
    
    print(f"Found {len(results)} players:")
    for player in results:
        print(f"- {player['name']} ({player['team']}) - {player['position'] or 'Unknown position'}")

def search_teams(dm: URLDataManager, query: str, sport: str = None):
    """Search for teams"""
    print(f"\n=== Team Search: '{query}' ===")
    if sport:
        print(f"Sport filter: {sport}")
    
    results = dm.search_teams(query, sport)
    
    if not results:
        print("No teams found")
        return
    
    print(f"Found {len(results)} teams:")
    for team in results:
        print(f"- {team['name']} ({team['abbreviation']}) - {team['sport']} - {team['games_count']} games")

def show_player_stats(dm: URLDataManager, player_id: int):
    """Show player statistics summary"""
    print(f"\n=== Player Stats Summary ===")
    
    summary = dm.get_player_stats_summary(player_id)
    
    if 'error' in summary:
        print(f"Error: {summary['error']}")
        return
    
    print(f"Player: {summary['player_name']}")
    print(f"Games played: {summary['games_played']}")
    
    if summary['stats']:
        print("\nStatistics:")
        for stat_name, stat_data in summary['stats'].items():
            print(f"  {stat_name.replace('_', ' ').title()}:")
            print(f"    Total: {stat_data['total']:.1f}")
            print(f"    Average: {stat_data['average']:.2f}")
            print(f"    Max: {stat_data['max']:.1f}")
            print(f"    Min: {stat_data['min']:.1f}")

def show_team_stats(dm: URLDataManager, team_id: int):
    """Show team statistics summary"""
    print(f"\n=== Team Stats Summary ===")
    
    summary = dm.get_team_stats_summary(team_id)
    
    if 'error' in summary:
        print(f"Error: {summary['error']}")
        return
    
    print(f"Team: {summary['team_name']}")
    print(f"Games played: {summary['games_played']}")
    print(f"Record: {summary['wins']}-{summary['losses']}")
    
    if summary['wins'] + summary['losses'] > 0:
        win_pct = summary['wins'] / (summary['wins'] + summary['losses'])
        print(f"Win percentage: {win_pct:.3f}")

def list_games(dm: URLDataManager, sport: str = None):
    """List stored games"""
    print(f"\n=== Stored Games ===")
    if sport:
        print(f"Sport filter: {sport}")
    
    games = dm.list_stored_games(sport)
    
    if not games:
        print("No games found")
        return
    
    print(f"Found {len(games)} games:")
    for game in games:
        print(f"- {game['away_team']} @ {game['home_team']} ({game['score']}) - {game['sport']} - {game['date']}")

def interactive_mode(dm: URLDataManager):
    """Run in interactive mode"""
    print("\n=== URL-based Sports Betting Analysis System ===")
    print("Available commands:")
    print("  1. preview <URL> - Preview data available from ESPN URL")
    print("  2. store <URL> - Store all data from URL")
    print("  3. store-select <URL> - Store selected stats from URL (interactive)")
    print("  4. search-players <QUERY> - Search for players")
    print("  5. search-teams <QUERY> [SPORT] - Search for teams")
    print("  6. player-stats <PLAYER_ID> - Show player statistics")
    print("  7. team-stats <TEAM_ID> - Show team statistics")
    print("  8. list-games [SPORT] - List stored games")
    print("  9. quit - Exit")
    
    while True:
        try:
            command = input("\nEnter command: ").strip().split()
            
            if not command:
                continue
            
            if command[0].lower() == 'quit':
                break
            elif command[0].lower() == 'preview' and len(command) > 1:
                preview_url(dm, command[1])
            elif command[0].lower() == 'store' and len(command) > 1:
                scrape_and_store_url(dm, command[1])
            elif command[0].lower() == 'store-select' and len(command) > 1:
                interactive_store_with_selection(dm, command[1])
            elif command[0].lower() == 'search-players' and len(command) > 1:
                search_players(dm, ' '.join(command[1:]))
            elif command[0].lower() == 'search-teams' and len(command) > 1:
                sport = command[2] if len(command) > 2 else None
                search_teams(dm, command[1], sport)
            elif command[0].lower() == 'player-stats' and len(command) > 1:
                try:
                    player_id = int(command[1])
                    show_player_stats(dm, player_id)
                except ValueError:
                    print("Please provide a valid player ID")
            elif command[0].lower() == 'team-stats' and len(command) > 1:
                try:
                    team_id = int(command[1])
                    show_team_stats(dm, team_id)
                except ValueError:
                    print("Please provide a valid team ID")
            elif command[0].lower() == 'list-games':
                sport = command[1] if len(command) > 1 else None
                list_games(dm, sport)
            else:
                print("Invalid command. Type 'quit' to exit.")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

def interactive_store_with_selection(dm: URLDataManager, url: str):
    """Interactive stat selection for storing"""
    print(f"\n=== Interactive Stat Selection ===")
    
    # First preview the URL
    preview = dm.preview_url(url)
    if 'error' in preview:
        print(f"Error: {preview['error']}")
        return
    
    available_stats = preview.get('available_betting_stats', {})
    if not available_stats:
        print("No betting stats available for this sport")
        return
    
    selected_stats = {}
    
    # Select player stats
    if 'player_stats' in available_stats:
        print(f"\nAvailable player stats: {', '.join(available_stats['player_stats'])}")
        player_input = input("Enter player stats to store (comma-separated, or 'all'): ").strip()
        
        if player_input.lower() == 'all':
            selected_stats['player_stats'] = available_stats['player_stats']
        elif player_input:
            selected_stats['player_stats'] = [s.strip() for s in player_input.split(',')]
    
    # Select team stats
    if 'team_stats' in available_stats:
        print(f"\nAvailable team stats: {', '.join(available_stats['team_stats'])}")
        team_input = input("Enter team stats to store (comma-separated, or 'all'): ").strip()
        
        if team_input.lower() == 'all':
            selected_stats['team_stats'] = available_stats['team_stats']
        elif team_input:
            selected_stats['team_stats'] = [s.strip() for s in team_input.split(',')]
    
    if not selected_stats:
        print("No stats selected, storing all available stats")
        selected_stats = None
    
    scrape_and_store_url(dm, url, selected_stats)

def main():
    parser = argparse.ArgumentParser(description='URL-based Sports Betting Analysis System')
    parser.add_argument('--preview', type=str, metavar='URL', help='Preview data from ESPN URL')
    parser.add_argument('--store', type=str, metavar='URL', help='Store all data from ESPN URL')
    parser.add_argument('--search-players', type=str, metavar='QUERY', help='Search for players')
    parser.add_argument('--search-teams', type=str, metavar='QUERY', help='Search for teams')
    parser.add_argument('--list-games', type=str, nargs='?', const='all', metavar='SPORT', help='List stored games')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')
    
    args = parser.parse_args()
    
    # If no arguments provided, show help
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    # Initialize data manager
    dm = URLDataManager()
    
    if args.preview:
        preview_url(dm, args.preview)
    
    if args.store:
        scrape_and_store_url(dm, args.store)
    
    if args.search_players:
        search_players(dm, args.search_players)
    
    if args.search_teams:
        search_teams(dm, args.search_teams)
    
    if args.list_games:
        sport = None if args.list_games == 'all' else args.list_games
        list_games(dm, sport)
    
    if args.interactive:
        interactive_mode(dm)

if __name__ == "__main__":
    main()
