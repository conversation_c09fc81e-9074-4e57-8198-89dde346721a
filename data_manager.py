"""
Data Manager for Sports Betting Analysis System
Handles database operations and data processing
"""
from database.models import (
    create_database, get_session, Team, Player, Game, 
    PlayerStat, TeamStat, BettingLine
)
from scrapers.espn_scraper import ESPNScraper
from sqlalchemy.orm import sessionmaker
from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta
import pandas as pd
from typing import List, Dict, Optional
import json

class DataManager:
    def __init__(self, db_path="sports_betting.db"):
        self.engine = create_database(db_path)
        self.Session = sessionmaker(bind=self.engine)
        self.scraper = ESPNScraper()
    
    def get_session(self):
        """Get a new database session"""
        return self.Session()
    
    def add_team(self, name: str, abbreviation: str, conference: str = None, 
                 division: str = None, city: str = None) -> Team:
        """Add a new team to the database"""
        session = self.get_session()
        try:
            # Check if team already exists
            existing_team = session.query(Team).filter_by(abbreviation=abbreviation).first()
            if existing_team:
                return existing_team
            
            team = Team(
                name=name,
                abbreviation=abbreviation,
                conference=conference,
                division=division,
                city=city
            )
            session.add(team)
            session.commit()
            return team
        finally:
            session.close()
    
    def add_player(self, name: str, team_abbreviation: str, position: str = None,
                   jersey_number: int = None, height: str = None, 
                   weight: int = None, age: int = None) -> Optional[Player]:
        """Add a new player to the database"""
        session = self.get_session()
        try:
            # Find team
            team = session.query(Team).filter_by(abbreviation=team_abbreviation).first()
            if not team:
                print(f"Team {team_abbreviation} not found")
                return None
            
            # Check if player already exists
            existing_player = session.query(Player).filter(
                and_(Player.name == name, Player.team_id == team.id)
            ).first()
            if existing_player:
                return existing_player
            
            player = Player(
                name=name,
                team_id=team.id,
                position=position,
                jersey_number=jersey_number,
                height=height,
                weight=weight,
                age=age
            )
            session.add(player)
            session.commit()
            return player
        finally:
            session.close()
    
    def add_game_from_scraper(self, game_data: Dict) -> Optional[Game]:
        """Add a game from scraper data"""
        session = self.get_session()
        try:
            # Check if game already exists
            if game_data.get('game_id'):
                existing_game = session.query(Game).filter_by(
                    espn_game_id=game_data['game_id']
                ).first()
                if existing_game:
                    return existing_game
            
            # Find teams
            home_team = session.query(Team).filter_by(
                name=game_data['home_team']
            ).first()
            away_team = session.query(Team).filter_by(
                name=game_data['away_team']
            ).first()
            
            if not home_team or not away_team:
                print(f"Teams not found: {game_data['home_team']} vs {game_data['away_team']}")
                return None
            
            # Parse date
            game_date = datetime.strptime(game_data['date'], '%Y%m%d')
            
            game = Game(
                espn_game_id=game_data.get('game_id'),
                date=game_date,
                home_team_id=home_team.id,
                away_team_id=away_team.id,
                home_score=game_data.get('scores', {}).get('home_score'),
                away_score=game_data.get('scores', {}).get('away_score'),
                status='final' if game_data.get('scores') else 'scheduled'
            )
            
            session.add(game)
            session.commit()
            return game
        finally:
            session.close()
    
    def scrape_and_store_games(self, start_date: str, end_date: str):
        """Scrape games from ESPN and store in database"""
        print(f"Scraping games from {start_date} to {end_date}...")
        
        games_data = self.scraper.scrape_date_range(start_date, end_date)
        
        stored_games = 0
        for game_data in games_data:
            game = self.add_game_from_scraper(game_data)
            if game:
                stored_games += 1
                
                # If game has an ID, try to get detailed boxscore
                if game_data.get('game_id') and game_data.get('scores'):
                    self.scrape_and_store_boxscore(game_data['game_id'])
        
        print(f"Stored {stored_games} games out of {len(games_data)} scraped")
    
    def scrape_and_store_boxscore(self, game_id: str):
        """Scrape detailed boxscore and store player/team stats"""
        boxscore_data = self.scraper.get_game_boxscore(game_id)
        
        if not boxscore_data:
            return
        
        session = self.get_session()
        try:
            game = session.query(Game).filter_by(espn_game_id=game_id).first()
            if not game:
                print(f"Game {game_id} not found in database")
                return
            
            # Store player stats
            for player_stat in boxscore_data.get('player_stats', []):
                self._store_player_stat(session, game, player_stat)
            
            # Store team stats
            team_stats = boxscore_data.get('team_stats', {})
            if team_stats:
                self._store_team_stats(session, game, team_stats)
            
            session.commit()
            
        finally:
            session.close()
    
    def _store_player_stat(self, session, game: Game, stat_data: Dict):
        """Store individual player statistics"""
        try:
            player_name = stat_data.get('player', stat_data.get('name', ''))
            if not player_name:
                return
            
            # Find player (this is simplified - you might need better matching)
            player = session.query(Player).filter_by(name=player_name).first()
            if not player:
                return
            
            # Check if stat already exists
            existing_stat = session.query(PlayerStat).filter(
                and_(PlayerStat.game_id == game.id, PlayerStat.player_id == player.id)
            ).first()
            if existing_stat:
                return
            
            # Parse stats (this will need adjustment based on actual ESPN format)
            player_stat = PlayerStat(
                game_id=game.id,
                player_id=player.id,
                minutes_played=self._parse_float(stat_data.get('min')),
                points=self._parse_int(stat_data.get('pts')),
                rebounds=self._parse_int(stat_data.get('reb')),
                assists=self._parse_int(stat_data.get('ast')),
                steals=self._parse_int(stat_data.get('stl')),
                blocks=self._parse_int(stat_data.get('blk')),
                turnovers=self._parse_int(stat_data.get('to')),
                fouls=self._parse_int(stat_data.get('pf'))
            )
            
            session.add(player_stat)
            
        except Exception as e:
            print(f"Error storing player stat: {e}")
    
    def _store_team_stats(self, session, game: Game, team_stats: Dict):
        """Store team statistics"""
        try:
            for stat_name, values in team_stats.items():
                if isinstance(values, dict) and 'home' in values and 'away' in values:
                    # Store home team stats
                    home_stat = TeamStat(
                        game_id=game.id,
                        team_id=game.home_team_id,
                        points=game.home_score
                    )
                    session.add(home_stat)
                    
                    # Store away team stats
                    away_stat = TeamStat(
                        game_id=game.id,
                        team_id=game.away_team_id,
                        points=game.away_score
                    )
                    session.add(away_stat)
                    
                    break  # Only create one record per team per game
                    
        except Exception as e:
            print(f"Error storing team stats: {e}")
    
    def _parse_int(self, value) -> Optional[int]:
        """Safely parse integer from string"""
        try:
            return int(value) if value and value != '-' else None
        except (ValueError, TypeError):
            return None
    
    def _parse_float(self, value) -> Optional[float]:
        """Safely parse float from string"""
        try:
            return float(value) if value and value != '-' else None
        except (ValueError, TypeError):
            return None
    
    def get_team_trends(self, team_abbreviation: str, last_n_games: int = 10) -> pd.DataFrame:
        """Get recent trends for a team"""
        session = self.get_session()
        try:
            team = session.query(Team).filter_by(abbreviation=team_abbreviation).first()
            if not team:
                return pd.DataFrame()
            
            # Get recent games
            recent_games = session.query(Game).filter(
                or_(Game.home_team_id == team.id, Game.away_team_id == team.id)
            ).filter(Game.status == 'final').order_by(desc(Game.date)).limit(last_n_games).all()
            
            games_data = []
            for game in recent_games:
                is_home = game.home_team_id == team.id
                opponent_id = game.away_team_id if is_home else game.home_team_id
                opponent = session.query(Team).get(opponent_id)
                
                team_score = game.home_score if is_home else game.away_score
                opponent_score = game.away_score if is_home else game.home_score
                
                games_data.append({
                    'date': game.date,
                    'opponent': opponent.abbreviation if opponent else 'Unknown',
                    'home_away': 'Home' if is_home else 'Away',
                    'team_score': team_score,
                    'opponent_score': opponent_score,
                    'win': team_score > opponent_score if team_score and opponent_score else None,
                    'point_differential': team_score - opponent_score if team_score and opponent_score else None
                })
            
            return pd.DataFrame(games_data)
            
        finally:
            session.close()
    
    def get_player_trends(self, player_name: str, last_n_games: int = 10) -> pd.DataFrame:
        """Get recent trends for a player"""
        session = self.get_session()
        try:
            player = session.query(Player).filter_by(name=player_name).first()
            if not player:
                return pd.DataFrame()
            
            # Get recent stats
            recent_stats = session.query(PlayerStat).join(Game).filter(
                PlayerStat.player_id == player.id
            ).order_by(desc(Game.date)).limit(last_n_games).all()
            
            stats_data = []
            for stat in recent_stats:
                stats_data.append({
                    'date': stat.game.date,
                    'opponent': 'TBD',  # Would need to calculate opponent
                    'minutes': stat.minutes_played,
                    'points': stat.points,
                    'rebounds': stat.rebounds,
                    'assists': stat.assists,
                    'steals': stat.steals,
                    'blocks': stat.blocks,
                    'turnovers': stat.turnovers
                })
            
            return pd.DataFrame(stats_data)
            
        finally:
            session.close()

# Initialize default NBA teams
def initialize_nba_teams(data_manager: DataManager):
    """Initialize database with NBA teams"""
    nba_teams = [
        ("Atlanta Hawks", "ATL", "Eastern", "Southeast"),
        ("Boston Celtics", "BOS", "Eastern", "Atlantic"),
        ("Brooklyn Nets", "BKN", "Eastern", "Atlantic"),
        ("Charlotte Hornets", "CHA", "Eastern", "Southeast"),
        ("Chicago Bulls", "CHI", "Eastern", "Central"),
        ("Cleveland Cavaliers", "CLE", "Eastern", "Central"),
        ("Dallas Mavericks", "DAL", "Western", "Southwest"),
        ("Denver Nuggets", "DEN", "Western", "Northwest"),
        ("Detroit Pistons", "DET", "Eastern", "Central"),
        ("Golden State Warriors", "GSW", "Western", "Pacific"),
        ("Houston Rockets", "HOU", "Western", "Southwest"),
        ("Indiana Pacers", "IND", "Eastern", "Central"),
        ("LA Clippers", "LAC", "Western", "Pacific"),
        ("Los Angeles Lakers", "LAL", "Western", "Pacific"),
        ("Memphis Grizzlies", "MEM", "Western", "Southwest"),
        ("Miami Heat", "MIA", "Eastern", "Southeast"),
        ("Milwaukee Bucks", "MIL", "Eastern", "Central"),
        ("Minnesota Timberwolves", "MIN", "Western", "Northwest"),
        ("New Orleans Pelicans", "NOP", "Western", "Southwest"),
        ("New York Knicks", "NYK", "Eastern", "Atlantic"),
        ("Oklahoma City Thunder", "OKC", "Western", "Northwest"),
        ("Orlando Magic", "ORL", "Eastern", "Southeast"),
        ("Philadelphia 76ers", "PHI", "Eastern", "Atlantic"),
        ("Phoenix Suns", "PHX", "Western", "Pacific"),
        ("Portland Trail Blazers", "POR", "Western", "Northwest"),
        ("Sacramento Kings", "SAC", "Western", "Pacific"),
        ("San Antonio Spurs", "SAS", "Western", "Southwest"),
        ("Toronto Raptors", "TOR", "Eastern", "Atlantic"),
        ("Utah Jazz", "UTA", "Western", "Northwest"),
        ("Washington Wizards", "WAS", "Eastern", "Southeast")
    ]
    
    for name, abbr, conference, division in nba_teams:
        data_manager.add_team(name, abbr, conference, division)
    
    print("Initialized NBA teams in database")
