# Sports Betting Analysis System

A comprehensive Python-based system for manual ESPN box score analysis with selective stat storage for sports betting insights.

## 🎯 **NEW: URL-Based System**

The system now works exactly as you requested:
- **Manual URL Input**: Paste ESPN box score URLs directly
- **Multi-Sport Support**: NHL, NBA, NFL, MLB
- **Selective Stat Storage**: Choose which betting-relevant stats to store
- **Database Indexing**: Search players and teams efficiently
- **Incremental Updates**: Add new games without duplicates

## Features

- **ESPN URL Processing**: Direct input of ESPN box score URLs
- **Multi-Sport Support**: NHL, NBA, NFL, MLB with sport-specific betting stats
- **Selective Data Storage**: Choose which statistics to store for analysis
- **SQLite Database**: Local storage with full indexing and search capabilities
- **Player/Team Search**: Fast search across all stored data
- **Betting-Focused Stats**: Pre-defined stats that are commonly bet on
- **Interactive CLI**: Easy-to-use command-line interface

## Project Structure

```
kamgambleaiV2/
├── database/
│   └── models.py              # SQLAlchemy database models (multi-sport)
├── scrapers/
│   └── espn_scraper.py        # URL-based ESPN box score scraper
├── analysis/
│   └── trend_analyzer.py      # Trend analysis and betting insights
├── url_data_manager.py        # URL-based data operations
├── url_main.py                # NEW: Main URL-based CLI
├── data_manager.py            # Legacy: Original data manager
├── main.py                    # Legacy: Original CLI
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## 🚀 **Quick Start (URL-Based System)**

### 1. **Setup** (if not already done):
```bash
pip install -r requirements.txt
python main.py --setup  # Initialize database with teams
```

### 2. **Preview a URL**:
```bash
python url_main.py --preview "https://www.espn.com/nhl/boxscore/_/gameId/401773322"
```

### 3. **Store Game Data**:
```bash
# Store all available stats
python url_main.py --store "https://www.espn.com/nhl/boxscore/_/gameId/401773322"

# Interactive mode for selective stat storage
python url_main.py --interactive
```

### 4. **Search and Analyze**:
```bash
# Search for players
python url_main.py --search-players "McDavid"

# Search for teams
python url_main.py --search-teams "Oilers" nhl

# List stored games
python url_main.py --list-games nhl
```

## 📊 **Betting-Relevant Stats by Sport**

### **NHL (Hockey)**
- **Player Stats**: goals, assists, points, shots, hits, blocked_shots, penalty_minutes, plus_minus, faceoff_wins, faceoff_attempts, time_on_ice
- **Goalie Stats**: saves, shots_against, goals_allowed, save_percentage, time_on_ice
- **Team Stats**: goals, shots, hits, blocked_shots, faceoff_percentage, penalty_minutes, power_play_goals, power_play_opportunities

### **NBA (Basketball)**
- **Player Stats**: points, rebounds, assists, steals, blocks, turnovers, field_goals_made, field_goals_attempted, three_pointers_made, three_pointers_attempted, free_throws_made, free_throws_attempted, minutes
- **Team Stats**: points, field_goal_percentage, three_point_percentage, free_throw_percentage, rebounds, assists, turnovers, steals, blocks

### **NFL (Football)**
- **Player Stats**: passing_yards, passing_touchdowns, interceptions, rushing_yards, rushing_touchdowns, receiving_yards, receiving_touchdowns, receptions, tackles, sacks
- **Team Stats**: total_yards, passing_yards, rushing_yards, turnovers, penalties, time_of_possession

### **MLB (Baseball)**
- **Player Stats**: hits, runs, rbis, home_runs, stolen_bases, strikeouts_batter, walks_batter, innings_pitched, strikeouts_pitcher, walks_pitcher, earned_runs
- **Team Stats**: runs, hits, errors, left_on_base

## Installation

1. **Clone or navigate to the project directory**:
   ```bash
   cd d:\kamgambleaiV2
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup the database**:
   ```bash
   python main.py --setup
   ```

## Usage

### Command Line Interface

The system provides several command-line options:

```bash
# Setup database and initialize NBA teams
python main.py --setup

# Scrape games from the last 7 days
python main.py --scrape 7

# Analyze a specific team (e.g., Lakers)
python main.py --analyze LAL

# Show recent trends for a team
python main.py --trends GSW

# Find betting opportunities
python main.py --opportunities

# Run in interactive mode
python main.py --interactive
```

### Interactive Mode

Run the system in interactive mode for easier exploration:

```bash
python main.py --interactive
```

Available interactive commands:
- `analyze <TEAM>` - Analyze team performance
- `trends <TEAM>` - Show recent game trends
- `opportunities` - Find betting opportunities
- `scrape <DAYS>` - Scrape recent games
- `quit` - Exit

### Team Abbreviations

Use standard NBA team abbreviations:
- LAL (Lakers), GSW (Warriors), BOS (Celtics), etc.

## Database Schema

The system uses SQLite with the following main tables:

- **Teams**: NBA team information
- **Players**: Player details and team associations
- **Games**: Game results and metadata
- **PlayerStats**: Individual player statistics per game
- **TeamStats**: Team statistics per game
- **BettingLines**: Betting odds and lines (for future use)

## Analysis Features

### Team Analysis
- Win/loss trends
- Scoring patterns
- Home vs away performance
- Point differential analysis
- Consistency ratings
- Recent form evaluation

### Player Analysis
- Scoring trends
- Statistical consistency
- Performance patterns
- Recent form tracking

### Betting Opportunities
The system identifies several types of opportunities:
- **Hot Streaks**: Teams on winning streaks with improving trends
- **Undervalued Teams**: Good performance metrics but poor recent record
- **Consistent Performers**: Reliable teams with steady performance

## Data Sources

- **ESPN**: Primary source for game schedules and box scores
- **Real-time scraping**: Respectful scraping with delays to avoid overloading servers

## Development Notes

### Adding New Sports
The system is designed primarily for NBA but can be extended:
1. Add new team data in `data_manager.py`
2. Modify scraper for different ESPN sport URLs
3. Adjust statistical fields in database models

### Customizing Analysis
Modify `trend_analyzer.py` to add new analytical methods:
- Custom trend calculations
- New opportunity detection algorithms
- Additional statistical metrics

### Database Management
- Database file: `sports_betting.db` (created automatically)
- Backup: Simply copy the database file
- Reset: Delete the database file and run `--setup` again

## Example Workflow

1. **Initial Setup**:
   ```bash
   python main.py --setup
   ```

2. **Scrape Recent Data**:
   ```bash
   python main.py --scrape 14  # Last 2 weeks
   ```

3. **Analyze Teams**:
   ```bash
   python main.py --analyze LAL
   python main.py --analyze GSW
   ```

4. **Find Opportunities**:
   ```bash
   python main.py --opportunities
   ```

5. **Interactive Exploration**:
   ```bash
   python main.py --interactive
   ```

## Limitations and Considerations

- **Rate Limiting**: Scraper includes delays to respect ESPN's servers
- **Data Availability**: Some historical data may not be available
- **Betting Responsibility**: This is for analysis only - bet responsibly
- **Legal Compliance**: Ensure sports betting is legal in your jurisdiction

## Future Enhancements

- Integration with betting APIs for real-time odds
- Machine learning models for prediction
- Web interface for easier visualization
- Support for additional sports (NFL, MLB, etc.)
- Advanced statistical models
- Real-time game tracking

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Scraping Failures**: ESPN may change their HTML structure
3. **Database Errors**: Check file permissions and disk space
4. **No Data Found**: Run scraping commands first

### Getting Help

Check the error messages for specific issues. Most problems are related to:
- Missing data (run scraping first)
- Invalid team abbreviations
- Network connectivity issues

## Contributing

To extend the system:
1. Follow the existing code structure
2. Add appropriate error handling
3. Include documentation for new features
4. Test with sample data before deploying

---

**Disclaimer**: This system is for educational and analytical purposes only. Always gamble responsibly and within legal boundaries.
